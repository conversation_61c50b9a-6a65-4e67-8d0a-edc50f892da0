../../Scripts/chroma.exe,sha256=cTja9dMR8WfcxPxj2pZYJ2ZYPzsLaRXF0GRvA3ro5Zc,108421
chromadb-1.0.12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
chromadb-1.0.12.dist-info/METADATA,sha256=PA2sA4NMHDaGCtkqAtF8FC3om04zoW0SPb8EGoxYlYY,7011
chromadb-1.0.12.dist-info/RECORD,,
chromadb-1.0.12.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb-1.0.12.dist-info/WHEEL,sha256=2OnzeD-Mt7eNjmrtchSJO1LUSaCwNHKze1SLnzTwXU8,94
chromadb-1.0.12.dist-info/entry_points.txt,sha256=vb__WRXGM9B5tQ1Ysrcp3LIgagWxYfyNsd0NS8C_08U,46
chromadb-1.0.12.dist-info/licenses/LICENSE,sha256=HrhfyXIkWY2tGFK11kg7vPCqhgh5DcxleloqdhrpyMY,11558
chromadb/__init__.py,sha256=oe608PyVYz8fQw_7KklGpfmwNb9uVXN6xIQRO5ymNKg,12993
chromadb/__pycache__/__init__.cpython-311.pyc,,
chromadb/__pycache__/app.cpython-311.pyc,,
chromadb/__pycache__/base_types.cpython-311.pyc,,
chromadb/__pycache__/config.cpython-311.pyc,,
chromadb/__pycache__/errors.cpython-311.pyc,,
chromadb/__pycache__/serde.cpython-311.pyc,,
chromadb/__pycache__/types.cpython-311.pyc,,
chromadb/api/__init__.py,sha256=AmbDgUBbTd6HDJFewq6d4wODzvcGgQUurLCucXMdp-8,23866
chromadb/api/__pycache__/__init__.cpython-311.pyc,,
chromadb/api/__pycache__/async_api.cpython-311.pyc,,
chromadb/api/__pycache__/async_client.cpython-311.pyc,,
chromadb/api/__pycache__/async_fastapi.cpython-311.pyc,,
chromadb/api/__pycache__/base_http_client.cpython-311.pyc,,
chromadb/api/__pycache__/client.cpython-311.pyc,,
chromadb/api/__pycache__/collection_configuration.cpython-311.pyc,,
chromadb/api/__pycache__/configuration.cpython-311.pyc,,
chromadb/api/__pycache__/fastapi.cpython-311.pyc,,
chromadb/api/__pycache__/rust.cpython-311.pyc,,
chromadb/api/__pycache__/segment.cpython-311.pyc,,
chromadb/api/__pycache__/shared_system_client.cpython-311.pyc,,
chromadb/api/__pycache__/types.cpython-311.pyc,,
chromadb/api/async_api.py,sha256=NzBFeuf_iSbR8TKKt4wT2nnSAaMsh4y4xa-rMcoRY4k,23981
chromadb/api/async_client.py,sha256=QVVhqJu-6AtXXX0Dt24eH8O_1_1eS8lH8Sy_s_9rCUo,17208
chromadb/api/async_fastapi.py,sha256=ySFojZ7hU_p1a_-B7ZL8WduKTL6huseHivJKRFkvWM4,23267
chromadb/api/base_http_client.py,sha256=PLTqbadscq1qnTIMoque-IIRjLx8cIM30zbl0vZUyIQ,3795
chromadb/api/client.py,sha256=iUfDANCnRJsxx1A9w0ZXId9BWXYjGskvlTsCpnbx-hU,16757
chromadb/api/collection_configuration.py,sha256=7cPtnZazy8LV80IoAmk2OPAoUNLxqtk2QyT-6MZkwrc,27564
chromadb/api/configuration.py,sha256=CxmL6G08ag6gPlsyH_PIicbSN63-4NnqDLGSzOxaK3Q,15583
chromadb/api/fastapi.py,sha256=l-tV4__2l-0nsU5ayv_AcJojoFBpD_kIpsnnFdYy1Ow,22486
chromadb/api/models/AsyncCollection.py,sha256=RysfxTC_3oJI4C_pvkFJEGUeK6Zywf99L-QDE1K2XDo,15724
chromadb/api/models/Collection.py,sha256=494PBefG_tX1W1YOm3dbPOxHEPuSQMb-a75CWcxqstk,15708
chromadb/api/models/CollectionCommon.py,sha256=rtlI52UWhyPGzBW_0rh4MpAp9D8VQutFB7ezsFcf-XY,19296
chromadb/api/models/__pycache__/AsyncCollection.cpython-311.pyc,,
chromadb/api/models/__pycache__/Collection.cpython-311.pyc,,
chromadb/api/models/__pycache__/CollectionCommon.cpython-311.pyc,,
chromadb/api/rust.py,sha256=8pGELVkBNFEafFT5wbLG7y7B0o9VOUUBseViP2YtHrA,19280
chromadb/api/segment.py,sha256=cpHvQ3nAzbkfY8I9sLms8tzqWCIw0lWsOfIB3zhudUs,35419
chromadb/api/shared_system_client.py,sha256=ERPE6-cP6S6GW9065b3HaxWQNnqr7_G76Fj7zOdRKT8,3595
chromadb/api/types.py,sha256=TpicGC3vvyCrJAniRY1GhRND8W0TP8fDV8gWGzDyRac,34255
chromadb/app.py,sha256=B2RVywncMIZTajX3r9oOk53U6_8MGQlLb4uGvV2LOrw,175
chromadb/auth/__init__.py,sha256=gI2ExiyIYVmjZ7uIDoOglqEa5vorcKkann-qPVkYCuw,8388
chromadb/auth/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/basic_authn/__init__.py,sha256=Ab6QLLV9fvFGqmmIQaKI0XtABMjJBm-DyJElpbYFYZ0,5238
chromadb/auth/basic_authn/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/simple_rbac_authz/__init__.py,sha256=5yDgrS8V1PCkm4Yhy0hyk-vR4fLamaMYWmJ9MnCAJU0,2718
chromadb/auth/simple_rbac_authz/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/token_authn/__init__.py,sha256=M5l7_dcibBXa-p2-EvMul7iSBr6oMAtle3MtK3IpAjM,8563
chromadb/auth/token_authn/__pycache__/__init__.cpython-311.pyc,,
chromadb/auth/utils/__init__.py,sha256=agrUgImTN09fCH3Wz56xTyJ-92JPglEeucAov7nZc4M,3493
chromadb/auth/utils/__pycache__/__init__.cpython-311.pyc,,
chromadb/base_types.py,sha256=VbX-mDb5sb2xCSwhRivUes9Xvw513RkVawfTwcvknSU,1296
chromadb/chromadb_rust_bindings.pyi,sha256=ps9KQUBuA2TMlvVaYL-98rGtBTLhHqpYfhq7VdNqY5U,5935
chromadb/cli/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/cli/__pycache__/__init__.cpython-311.pyc,,
chromadb/cli/__pycache__/cli.cpython-311.pyc,,
chromadb/cli/__pycache__/utils.cpython-311.pyc,,
chromadb/cli/cli.py,sha256=BqXgIGWNfkiTVwwQucJ-_4nZCSbBtqAbM3ANI0glkTI,1549
chromadb/cli/utils.py,sha256=AzxNgX_GtgXU3DvZVwFQ8nuh0_usd4v6_ttOB4HeZLQ,1287
chromadb/config.py,sha256=qn9E6-KpXmn5VjhP8GrBy6w7K9iwXUEaSEwEJl_6_ww,18998
chromadb/db/__init__.py,sha256=tANHwrFzaK31HW4DeeHbt-enZtPMVoKvGirWIVxADCE,3162
chromadb/db/__pycache__/__init__.cpython-311.pyc,,
chromadb/db/__pycache__/base.cpython-311.pyc,,
chromadb/db/__pycache__/migrations.cpython-311.pyc,,
chromadb/db/__pycache__/system.cpython-311.pyc,,
chromadb/db/base.py,sha256=NsfSrNfo00nCljF9OLvYbbZFSRKNaJJvosPKOtp8WMQ,5945
chromadb/db/impl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/db/impl/__pycache__/__init__.cpython-311.pyc,,
chromadb/db/impl/__pycache__/sqlite.cpython-311.pyc,,
chromadb/db/impl/__pycache__/sqlite_pool.cpython-311.pyc,,
chromadb/db/impl/grpc/__pycache__/client.cpython-311.pyc,,
chromadb/db/impl/grpc/__pycache__/server.cpython-311.pyc,,
chromadb/db/impl/grpc/client.py,sha256=mmkoAA2rmP8vTacRXyORx4xmc1T-5eDAtNK8bDa4Oh8,20808
chromadb/db/impl/grpc/server.py,sha256=fCWQAgNxJHK1m-dLmvttSZUztkQSxQRpZB9pTdIbWXs,21189
chromadb/db/impl/sqlite.py,sha256=uwIgjHsREbSKTVdFQdezMmH093XYthWZ-twk0431-nE,9700
chromadb/db/impl/sqlite_pool.py,sha256=jGbFeQL99eQ2tyf-Eh9YuZayzrLScZTEsRix532mQ_o,5535
chromadb/db/migrations.py,sha256=BepOJSL8A4djpmeR11tTKidOtbn-x2dSFz2QbdZdTNM,9906
chromadb/db/mixins/__pycache__/embeddings_queue.cpython-311.pyc,,
chromadb/db/mixins/__pycache__/sysdb.cpython-311.pyc,,
chromadb/db/mixins/embeddings_queue.py,sha256=sIwl6PJIZJckTbmZkHSIlXAYGNB3TdvfEJ_LHtqPKR4,19832
chromadb/db/mixins/sysdb.py,sha256=zmXkhBgnDl67hGxhLRejlnESFotd1S_U8doaWRthHbU,37560
chromadb/db/system.py,sha256=TggD79uRmxa24yySQrM_kPZEewlBpZjr76Ez3_ixIw8,6322
chromadb/errors.py,sha256=YMzdOgNqvoM7659MG970c9kp3dyW76-vH3hANGhbN-M,4212
chromadb/execution/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/execution/__pycache__/__init__.cpython-311.pyc,,
chromadb/execution/executor/__pycache__/abstract.cpython-311.pyc,,
chromadb/execution/executor/__pycache__/distributed.cpython-311.pyc,,
chromadb/execution/executor/__pycache__/local.cpython-311.pyc,,
chromadb/execution/executor/abstract.py,sha256=dFzh7ji9EhqIc2iI3siEyYipXhRVEY_6q4KQ3ckwkE4,489
chromadb/execution/executor/distributed.py,sha256=AwtJGB9dP-bpc6JXN8CzAL66MuSwQwk_apzG6ggQz-k,9277
chromadb/execution/executor/local.py,sha256=_cZaUsUE90JB1UJgCMVvmVK4Hj5_kT6wFDTLmw3uhPM,7723
chromadb/execution/expression/__pycache__/operator.cpython-311.pyc,,
chromadb/execution/expression/__pycache__/plan.cpython-311.pyc,,
chromadb/execution/expression/operator.py,sha256=zD17Dzs3nN6AUGTkMA-297E8UbLsBBg1-7GldDrVfYc,1508
chromadb/execution/expression/plan.py,sha256=F_g9zCuaqMJWm5ESbilCqO8_WMvDXazyb1ab5_Xn0Yg,574
chromadb/experimental/density_relevance.ipynb,sha256=ks8SSD1jH8ChjfS9fmBeMKXjpNHnF8heF9juWRuDm8o,369242
chromadb/ingest/__init__.py,sha256=aRKfCkjWqzkCep9XpoIRjRxhu7n8O-faEo2WmcfEFys,4420
chromadb/ingest/__pycache__/__init__.cpython-311.pyc,,
chromadb/ingest/impl/__pycache__/utils.cpython-311.pyc,,
chromadb/ingest/impl/utils.py,sha256=NgsUC0RPrd5LQMk1BhVRXDE_xiEQ6Z8SjAdrbjzlkUw,1882
chromadb/log_config.yml,sha256=y6lDM7gCNcSmtuAL6nIkaTNZvdwsIz6YLvoep4nkRuA,958
chromadb/logservice/__pycache__/logservice.cpython-311.pyc,,
chromadb/logservice/logservice.py,sha256=MZW45v6mg7bcSOnjMPHHIsJJVNjATm5LxMNw-5ShI50,6051
chromadb/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/migrations/__pycache__/__init__.cpython-311.pyc,,
chromadb/migrations/embeddings_queue/00001-embeddings.sqlite.sql,sha256=yXegLSX1rymqnDQNfhhWUnzf_P724alduquFBVzGScA,271
chromadb/migrations/embeddings_queue/00002-embeddings-queue-config.sqlite.sql,sha256=4ps1zRXbaD2yqgfcT0wFo99Afi1pK1MPkAZpdczdUSQ,99
chromadb/migrations/metadb/00001-embedding-metadata.sqlite.sql,sha256=DSFWXw0aQ-5O-er1DwjG1uWS1gFK1U3-IrMdzy2qMCI,624
chromadb/migrations/metadb/00002-embedding-metadata.sqlite.sql,sha256=Dkd8KxRG8R2pMJKNjFmL4tJE2UeVcmIv0sb_ANORVgY,339
chromadb/migrations/metadb/00003-full-text-tokenize.sqlite.sql,sha256=90gDZf9YDGALlnbyurEn-T8MsHDkwNBgnxRwDZVc_Es,239
chromadb/migrations/metadb/00004-metadata-indices.sqlite.sql,sha256=nVKGTYqUNqLKxo0x3GqtBIhnbAaeo6E5kYCrAW8vscY,390
chromadb/migrations/metadb/00005-max-seq-id-int.sqlite.sql,sha256=7IEzqfPPAaLS2CRfbXD65McVqFwQhWBQDnHQEGHT6x8,1476
chromadb/migrations/sysdb/00001-collections.sqlite.sql,sha256=ItDox_ib7k9dngqibUNSPVRtHltrznIrVaoXAdl5AYw,370
chromadb/migrations/sysdb/00002-segments.sqlite.sql,sha256=MiE1dmc3SUJJiyULPHLWQdfA5bzO-j27jMDUr-NzYas,401
chromadb/migrations/sysdb/00003-collection-dimension.sqlite.sql,sha256=oQXqpTbKO9mCXotFpREEoK-pK2yRKRp0XuMKCkLOWN8,55
chromadb/migrations/sysdb/00004-tenants-databases.sqlite.sql,sha256=-nlyv0U6_rXdbPkkuGV4ZwMEa3wWd6nC0BXdw77k3hA,1257
chromadb/migrations/sysdb/00005-remove-topic.sqlite.sql,sha256=FspWWbQu0rJ8WV9Cc2DozVTto0LxiHyXpDU7QChFXhE,156
chromadb/migrations/sysdb/00006-collection-segment-metadata.sqlite.sql,sha256=AfQ9XlYn8QbxEXr0EMYOHZqfHiG09f65pVFJckHQ9Rw,402
chromadb/migrations/sysdb/00007-collection-config.sqlite.sql,sha256=Ri2DxB8iIcnxxlNtXM2sHZel3abL48_pvuSNh30MIos,108
chromadb/migrations/sysdb/00008-maintenance-log.sqlite.sql,sha256=mFVVaCvrkO39V8qKWxd2MT7QCiycoZgWqZgtvQ1jjzA,255
chromadb/migrations/sysdb/00009-segment-collection-not-null.sqlite.sql,sha256=isC6vg28aZDppxLYERNIBcluyIiwjKsxJx-cAGxH34s,338
chromadb/proto/.gitignore,sha256=Ss9RnWXKRkXGGRKOPRkyd4EDhgv3ZfsseJ2faiTiBUg,26
chromadb/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/proto/__pycache__/__init__.cpython-311.pyc,,
chromadb/proto/__pycache__/convert.cpython-311.pyc,,
chromadb/proto/__pycache__/utils.cpython-311.pyc,,
chromadb/proto/convert.py,sha256=7RtiFC9XuUV3kyGozFusmVxCZ0wcjHPImkW5bS50fDI,27303
chromadb/proto/utils.py,sha256=vr37wPgIbsde1RR11fvoNAzqVZVLI0_jmkVrDPcugSY,2638
chromadb/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/quota/__init__.py,sha256=ZdUCiMPGTA83l1omghw043cZnM3b01jiqe_8sG8Aww8,1888
chromadb/quota/__pycache__/__init__.cpython-311.pyc,,
chromadb/quota/simple_quota_enforcer/__init__.py,sha256=chUn4f5kYjXCdI-qG1wawBKuLMGmwzlzWENLLX6EeCM,1549
chromadb/quota/simple_quota_enforcer/__pycache__/__init__.cpython-311.pyc,,
chromadb/rate_limit/__init__.py,sha256=YhPyWqXqvpFpHnTqeA_2PuBRNfRdpkYZPdI35n3r9gA,925
chromadb/rate_limit/__pycache__/__init__.cpython-311.pyc,,
chromadb/rate_limit/simple_rate_limit/__init__.py,sha256=S6MJoR9mrCVcCUKSAfBNWisk9nmCKPck8jC6lEr_p80,1219
chromadb/rate_limit/simple_rate_limit/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/__init__.py,sha256=eODfbn9NFgwsnzVlyMtgbV4dtEVJuF2esotIrWHjNLg,4145
chromadb/segment/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/distributed/__init__.py,sha256=cWpsMpo4uQnPMVVflML1C3JE4jSBQqWZ6ZwhfUgu_kM,2783
chromadb/segment/distributed/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/distributed/__pycache__/segment_directory.cpython-311.pyc,,
chromadb/segment/impl/distributed/segment_directory.py,sha256=eC_8i235YBDMbKNLBVdzPv7wMUFBQXYfELAzDHI6nXs,13730
chromadb/segment/impl/manager/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/manager/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/manager/__pycache__/distributed.cpython-311.pyc,,
chromadb/segment/impl/manager/__pycache__/local.cpython-311.pyc,,
chromadb/segment/impl/manager/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/segment/impl/manager/cache/__pycache__/__init__.cpython-311.pyc,,
chromadb/segment/impl/manager/cache/__pycache__/cache.cpython-311.pyc,,
chromadb/segment/impl/manager/cache/cache.py,sha256=f6TjCvWkke2NH6LK9q2HvhAFrOmhMbPAhIRwqRscnRw,3554
chromadb/segment/impl/manager/distributed.py,sha256=AYEgGf8KlXptQ2zJ-RHG7r6-7w1eEeuH8VwLsY7Enes,3083
chromadb/segment/impl/manager/local.py,sha256=psVULgN6hfUv_jQPAp2c2z187OGqWNwhmaCtV6PuW-o,10862
chromadb/segment/impl/metadata/__pycache__/sqlite.cpython-311.pyc,,
chromadb/segment/impl/metadata/sqlite.py,sha256=QP4RheEfXTNy9kUCA9jK7byCAinNzs5MyRfRefLamrs,26509
chromadb/segment/impl/vector/__pycache__/batch.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/brute_force_index.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/hnsw_params.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/local_hnsw.cpython-311.pyc,,
chromadb/segment/impl/vector/__pycache__/local_persistent_hnsw.cpython-311.pyc,,
chromadb/segment/impl/vector/batch.py,sha256=0ucpl0L4M1CYk0ihl7moIRRHr-2i4dQ3QrTiFGN7veA,4319
chromadb/segment/impl/vector/brute_force_index.py,sha256=g03ZD1ZaVfUrjFBlOP_XoDYMf5YyzJ6OBuG6CVPpsgY,5571
chromadb/segment/impl/vector/hnsw_params.py,sha256=oJcHMNkB_Pwjp9l3ayXzpYobAZikBZVVn8qoQQbGhi8,3250
chromadb/segment/impl/vector/local_hnsw.py,sha256=-a0U4CVdjl10jHBtvl1PmhPEzNJpSUGkuLkUBFJTRto,12378
chromadb/segment/impl/vector/local_persistent_hnsw.py,sha256=yMFtM6f8AKjyMp9lMdY74Ur_FhLH80OtIZVtoZngtzk,22692
chromadb/serde.py,sha256=otSk0Xh9h1gIzegXUXne_ke2w9cXNB4o4dxAq0fAJbU,1552
chromadb/server/__init__.py,sha256=_VbOGchxMmMlSC1Z8SzcL6J7w6uOelqV-X8vR4UPKv4,181
chromadb/server/__pycache__/__init__.cpython-311.pyc,,
chromadb/server/fastapi/__init__.py,sha256=tv1OcG5yRBaF1WQgBmywVOyXXLZ6quDllUr6Sif9218,72857
chromadb/server/fastapi/__pycache__/__init__.cpython-311.pyc,,
chromadb/server/fastapi/__pycache__/types.cpython-311.pyc,,
chromadb/server/fastapi/types.py,sha256=gqIw15PTCczGwlKL77jpATHXJ5g8UhfbuHzxWqzjfgg,2584
chromadb/telemetry/README.md,sha256=O9nVNPAMEb2tXTUBsHMZZ9dNkLVzzqu2x5a9Mz-jr04,597
chromadb/telemetry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/telemetry/__pycache__/__init__.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/__init__.py,sha256=ZHb1lsOeggtdriz_Gb4c7BW_C2L8fUR7V66WIqcoA_c,6208
chromadb/telemetry/opentelemetry/__pycache__/__init__.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/__pycache__/fastapi.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/__pycache__/grpc.cpython-311.pyc,,
chromadb/telemetry/opentelemetry/fastapi.py,sha256=8LFsxJcauSzR3wOx1cFDlNhveI-6RNKw2upblvnVWVw,415
chromadb/telemetry/opentelemetry/grpc.py,sha256=BDlkM3DKo3rbD0Nwpawyur2Dv8wtvQdZMju2eCZOt4o,3826
chromadb/telemetry/product/__init__.py,sha256=ZGDTlOfVL-GbFMBsu0ea2D-ZzWJK7rSkJI9ShADfsHA,3033
chromadb/telemetry/product/__pycache__/__init__.cpython-311.pyc,,
chromadb/telemetry/product/__pycache__/events.cpython-311.pyc,,
chromadb/telemetry/product/__pycache__/posthog.cpython-311.pyc,,
chromadb/telemetry/product/events.py,sha256=IfJ7GH_akJ8f9m4lcY1rNZHr-H6YQqx9V_Xfe_-6J9E,8921
chromadb/telemetry/product/posthog.py,sha256=4km1IvH4wf1M0dQFLqzP76bs_Y0DKJl-sELI31-gopU,2237
chromadb/test/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/test/__pycache__/__init__.cpython-311.pyc,,
chromadb/test/__pycache__/conftest.cpython-311.pyc,,
chromadb/test/__pycache__/test_api.cpython-311.pyc,,
chromadb/test/__pycache__/test_chroma.cpython-311.pyc,,
chromadb/test/__pycache__/test_cli.cpython-311.pyc,,
chromadb/test/__pycache__/test_client.cpython-311.pyc,,
chromadb/test/__pycache__/test_config.cpython-311.pyc,,
chromadb/test/__pycache__/test_multithreaded.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_collection.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_delete_database.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_get_database.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_invalid_update.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_limit_offset.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_list_databases.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_numpy_list_inputs.cpython-311.pyc,,
chromadb/test/api/__pycache__/test_types.cpython-311.pyc,,
chromadb/test/api/test_collection.py,sha256=bNKBCOullR-9zrvLAVIaiiPWR9syQ3MdjgOQ04FQMtU,2262
chromadb/test/api/test_delete_database.py,sha256=3Xt7krOab0jS_8Oj9ZcKfAuENMMBs3k4e7XhMkh0n1k,2753
chromadb/test/api/test_get_database.py,sha256=t0anbly2EDezRlm3sl2cwLQDW-cJMlkm0hGKr8a_XnI,298
chromadb/test/api/test_invalid_update.py,sha256=81Iq-pOC5tb3wvxHVE8j5a8ef2prVQnGG-FpdIc2_vk,603
chromadb/test/api/test_limit_offset.py,sha256=Sw0-zsKgUtWK7b0kkUHpoJIQmtjbmJguIke3xHpBvP4,2091
chromadb/test/api/test_list_databases.py,sha256=t1SXbxkhghJweqCTlFcXLFntlSHbmFh2OflQobN3EVY,3195
chromadb/test/api/test_numpy_list_inputs.py,sha256=ssCiOTkO0XaMlMEMjvH9dUGghdTSLCuqJbh3XHlYjlU,2147
chromadb/test/api/test_types.py,sha256=P27zFtLw3lovxf7C7PufH7Q-OAjx5PahU7HDRlhUIqg,3324
chromadb/test/auth/__pycache__/test_auth_utils.cpython-311.pyc,,
chromadb/test/auth/test_auth_utils.py,sha256=u_QnOzVkOKbgUJggE-L2xAfqWM7ENfWXeDsNOIR6-iM,3126
chromadb/test/client/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
chromadb/test/client/__pycache__/__init__.cpython-311.pyc,,
chromadb/test/client/__pycache__/create_http_client_with_basic_auth.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_cloud_client.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_create_http_client.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_database_tenant.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_database_tenant_auth.cpython-311.pyc,,
chromadb/test/client/__pycache__/test_multiple_clients_concurrency.cpython-311.pyc,,
chromadb/test/client/create_http_client_with_basic_auth.py,sha256=Zq6Yjs0EjgpcmE9s6dB0bfvPHFqdxwzjjJrGaKimrog,814
chromadb/test/client/test_cloud_client.py,sha256=-lRvImdtB7wUAGkCBxt-Vf37CWeWaZ1-GOCtU3BqI_Y,3184
chromadb/test/client/test_create_http_client.py,sha256=9pbHdJ_rUj6U3609SNGfrcDGi4Hpb9SG5l1DlmgJUaM,572
chromadb/test/client/test_database_tenant.py,sha256=jsbzy1aSbsKRSITz696rAyK6pI3D9cpnVASmV1ptlyQ,7355
chromadb/test/client/test_database_tenant_auth.py,sha256=CT9c_e92OUPB3aWVG8HoL1YorNURLzsu7NMW9x8z234,3260
chromadb/test/client/test_multiple_clients_concurrency.py,sha256=hx3ISUBCgztRVieciI9IS_0nkNXpXBXMwh_g1dHih94,2055
chromadb/test/configurations/__pycache__/test_collection_configuration.cpython-311.pyc,,
chromadb/test/configurations/__pycache__/test_configurations.cpython-311.pyc,,
chromadb/test/configurations/test_collection_configuration.py,sha256=_4OGPJqmpa21vPGieW4zhNFYit5yFjTOsfCaXejipM4,45030
chromadb/test/configurations/test_configurations.py,sha256=TzyTzPpYzigrVcBYlfGrmgE60W3uLOJi-WH-o_POt8M,3790
chromadb/test/conftest.py,sha256=RdOotWjaAIxciAnWDajnQJG3q8PMmELugDxeiqOYPVI,37027
chromadb/test/data_loader/__pycache__/test_data_loader.cpython-311.pyc,,
chromadb/test/data_loader/test_data_loader.py,sha256=80m62vUH9sSLFEMrq9ibrAwW7Vih83ycwEoUrsSIOHY,3830
chromadb/test/db/__pycache__/test_log_purge.cpython-311.pyc,,
chromadb/test/db/test_log_purge.py,sha256=RGPFjnYrIgeTTpkkiyAMfgPP-74WvJ7hr7gjs3cjc3c,1823
chromadb/test/distributed/README.md,sha256=fwQFXTGy-sIvTmmieTzQJA49ohgdirx9P9AoznwFjxc,236
chromadb/test/distributed/__pycache__/test_log_failover.cpython-311.pyc,,
chromadb/test/distributed/__pycache__/test_reroute.cpython-311.pyc,,
chromadb/test/distributed/__pycache__/test_sanity.cpython-311.pyc,,
chromadb/test/distributed/test_log_failover.py,sha256=OL94mxDAXoYxv2imRfgW9UHJqI0JVjL6FPcWwpt9kI8,10795
chromadb/test/distributed/test_reroute.py,sha256=vRmckCusEopqn-aOvvqoqOg26i1hlPAo7UChdSf3Rj8,2516
chromadb/test/distributed/test_sanity.py,sha256=Nigojq8IQHdWXA9xtKpN3XfhmoagbN7Qnm3QjV388qE,2984
chromadb/test/ef/__pycache__/test_custom_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_default_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_multimodal_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_ollama_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_onnx_mini_lm_l6_v2.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_openai_ef.cpython-311.pyc,,
chromadb/test/ef/__pycache__/test_voyageai_ef.cpython-311.pyc,,
chromadb/test/ef/test_custom_ef.py,sha256=FrEoV6jM0ayEARH5noaSsZVdjs2W7B4R4R0rBi51Wkk,3398
chromadb/test/ef/test_default_ef.py,sha256=-b-WygqhPgGj0HXzlLoovpvcX-HFGgPo1LSit_TbjLo,2750
chromadb/test/ef/test_ef.py,sha256=lp-7LJWrnVJXSecp5er7gRv80brX0QbDqmNtKd86lGw,3502
chromadb/test/ef/test_multimodal_ef.py,sha256=KdS6bJWe8-8yd9XUrSiktBab_qa2OYsKzHUxjXcoDYg,6343
chromadb/test/ef/test_ollama_ef.py,sha256=UVG8Zxxy-q1LSNJiLp3KRqy3SH1t2glZDG6DmIm4kuA,1885
chromadb/test/ef/test_onnx_mini_lm_l6_v2.py,sha256=w3waXSCQ7_G2bKV9ckwXzYrqJdZAlQYITmdhJyGHMIc,7746
chromadb/test/ef/test_openai_ef.py,sha256=1OkvA_srh9I9jAmXg64v0zx64gWbvodgWDnHt6qU9bU,1284
chromadb/test/ef/test_voyageai_ef.py,sha256=QgxIGza1M9vo-rhvY6KAt_DvRSa0lLL6IgHLqhXwz4U,622
chromadb/test/openssl.cnf,sha256=lcaXW8qo1ql7AsI2us5KGyT_49glqD3OCLFYye0YPz4,201
chromadb/test/property/__pycache__/invariants.cpython-311.pyc,,
chromadb/test/property/__pycache__/strategies.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_add.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_client_url.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_collections.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_collections_with_database_tenant.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_collections_with_database_tenant_overwrite.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_cross_version_persist.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_embeddings.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_filtering.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_fork.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_persist.cpython-311.pyc,,
chromadb/test/property/__pycache__/test_restart_persist.cpython-311.pyc,,
chromadb/test/property/invariants.py,sha256=j2UWQ7iuz4tLYCtsR66Zs4pZjotNBbSdfgEsAormHJw,23440
chromadb/test/property/strategies.py,sha256=Y8gYr-1CB0P63OUd86HdYpwnovocrR47qf_4nS7umyk,25282
chromadb/test/property/test_add.py,sha256=qx8-kf5tdCAzhWiJOZI7mBhgn7zhaf6q1G2Y0BYPQwI,11280
chromadb/test/property/test_client_url.py,sha256=X51GnpgX9nZ1yK1-Q_Tpccg02KdtWJr-EIx26Gb59ug,4015
chromadb/test/property/test_collections.py,sha256=pwLNmbCf3Kx90KQpYaVxSIgfey1l8Oym4WZscTJBN_4,12665
chromadb/test/property/test_collections_with_database_tenant.py,sha256=9q5_KJRi7i3Du6D-9RrqUTwNIO1yxzTQ9aVk46RaohQ,6199
chromadb/test/property/test_collections_with_database_tenant_overwrite.py,sha256=oHH3HSBpDG9ysq2-XMziOrC5EJCqW3rriE0cjjpbHgQ,7927
chromadb/test/property/test_cross_version_persist.py,sha256=A28nX59txIpnmxvOcM27fwcjeLpUJ40oleE24xllfg4,13919
chromadb/test/property/test_embeddings.py,sha256=Xpv0CRNPsKlAqgH7RkVf8hRciv7BPxLsogGlIZ616RA,53502
chromadb/test/property/test_filtering.py,sha256=tm7OdS4_nC_oG4aVAXCOj1kMnKHmjrHHzAdcEsKbDQo,20655
chromadb/test/property/test_fork.py,sha256=FB9TbgRMg-RXJxfH59gqbfQHuMx4WICEO3JxrOcY6OI,8022
chromadb/test/property/test_persist.py,sha256=lFQUtdFwXmq4gQjoyKSv5-ZeaEHDD3w18fAig4OuM8Q,24298
chromadb/test/property/test_restart_persist.py,sha256=aM6z0EL6UB2fm3zHPMwTxeEY9UsMoXicfI_Wy3SltFg,3433
chromadb/test/segment/distributed/__pycache__/test_memberlist_provider.cpython-311.pyc,,
chromadb/test/segment/distributed/__pycache__/test_rendezvous_hash.cpython-311.pyc,,
chromadb/test/segment/distributed/test_memberlist_provider.py,sha256=kJm9gA7vCn5dEBXDCupD9cYM6txlOax7wSKrVu9mc5A,3959
chromadb/test/segment/distributed/test_rendezvous_hash.py,sha256=DW9HjvWUBdFoYx4fFtJiqRr-EbyZJRZhX5MxwVAYNf4,2317
chromadb/test/stress/__pycache__/test_many_collections.cpython-311.pyc,,
chromadb/test/stress/test_many_collections.py,sha256=mKgEPZayf6czuxFSKCsMCQFO_ZfehafwRrXhThTmFWM,1154
chromadb/test/test_api.py,sha256=YLc3hsaVT79uG73pfF4rNrEHjPG2wXlphLQhMJMKRUM,56807
chromadb/test/test_chroma.py,sha256=D7n4cQzwBdutGKzy9ryoAH7DqM3bz4UQLdrI9O2zOAc,4445
chromadb/test/test_cli.py,sha256=WRXYdQVROsdEd_c_mL605LCWpaVIbJTS8Fki4so-TF8,5206
chromadb/test/test_client.py,sha256=VnZBdWVS4ZwfRPuDyrzf11Utlxb4DuwfP3wwIveIs88,3675
chromadb/test/test_config.py,sha256=lLrxkV4lroIBXv9bVYA6--xxrUpyt6CgEOOF30rTdjo,4318
chromadb/test/test_multithreaded.py,sha256=DLQ-HWJaGwwJMw2z3TuKR2aApIzDaHnODRMkSAUSjxc,8774
chromadb/test/utils/__pycache__/cross_version.cpython-311.pyc,,
chromadb/test/utils/__pycache__/distance_functions.cpython-311.pyc,,
chromadb/test/utils/__pycache__/test_embedding_function_schemas.cpython-311.pyc,,
chromadb/test/utils/__pycache__/test_result_df_transform.cpython-311.pyc,,
chromadb/test/utils/__pycache__/wait_for_version_increase.cpython-311.pyc,,
chromadb/test/utils/cross_version.py,sha256=eP-w46iXYtbROsjTepsExy1G-LkruhgUowG7yn25g2o,2319
chromadb/test/utils/distance_functions.py,sha256=tFCiDMkFLR0cuMKBa-RxgHuKt3efoi0RrM76DSrwo1g,191
chromadb/test/utils/test_embedding_function_schemas.py,sha256=gId8PEqZJrKzuBFl4voLfsVRaNpXrPP7hk0EZ8fHRd0,4758
chromadb/test/utils/test_result_df_transform.py,sha256=BLPhIxhJOsKS8EHGL6P4qHUldsBtIj0nG9dD75h9K6A,6130
chromadb/test/utils/wait_for_version_increase.py,sha256=1bGROGFbjaKVGZYePox5u0GNZH3MnwDdrvbGCLmPWVY,902
chromadb/types.py,sha256=yHQIP-pQsFT0c7caxmrv5oBUSHJPFGk0TpStZWi8lKs,9500
chromadb/utils/__init__.py,sha256=IZ6_vjQ2Qrdkr12ZDZnulDovxD_futgAzhQnm9nNYwo,390
chromadb/utils/__pycache__/__init__.cpython-311.pyc,,
chromadb/utils/__pycache__/async_to_sync.cpython-311.pyc,,
chromadb/utils/__pycache__/batch_utils.cpython-311.pyc,,
chromadb/utils/__pycache__/data_loaders.cpython-311.pyc,,
chromadb/utils/__pycache__/delete_file.cpython-311.pyc,,
chromadb/utils/__pycache__/directory.cpython-311.pyc,,
chromadb/utils/__pycache__/distance_functions.cpython-311.pyc,,
chromadb/utils/__pycache__/fastapi.cpython-311.pyc,,
chromadb/utils/__pycache__/lru_cache.cpython-311.pyc,,
chromadb/utils/__pycache__/messageid.cpython-311.pyc,,
chromadb/utils/__pycache__/read_write_lock.cpython-311.pyc,,
chromadb/utils/__pycache__/rendezvous_hash.cpython-311.pyc,,
chromadb/utils/__pycache__/results.cpython-311.pyc,,
chromadb/utils/async_to_sync.py,sha256=-tzc2OvCLk2u8tDRk_ZYySNM7m9HTFvYXqqpwjoDDi4,1741
chromadb/utils/batch_utils.py,sha256=IWG4BvXG9d5kHPvrtM_wsXNJpj_FfZrenrPYfIicRf4,1275
chromadb/utils/data_loaders.py,sha256=75HsZdhGdb6ZOzKXM85Zj2o5_C2ehTM_so8OhDhCIBU,1418
chromadb/utils/delete_file.py,sha256=3ktMqjkvIuwpFJL5xTOP87V7DG8k9s4RVsuKQXJNA-g,1168
chromadb/utils/directory.py,sha256=o0SfnZhZ6bf-nLEFwG9LQTHWkxtxJvEioTPx6y7jga4,644
chromadb/utils/distance_functions.py,sha256=YQ8mbG5EhxmvGKiQekSJpmtmGsRKf3VpojAqYKDAA78,989
chromadb/utils/embedding_functions/__init__.py,sha256=jscbnQ7-pq8-iCgJDqXOQ_22AHRCx-mOSFX9EYVJPzw,8341
chromadb/utils/embedding_functions/__pycache__/__init__.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/amazon_bedrock_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/baseten_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/chroma_langchain_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/cloudflare_workers_ai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/cohere_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/google_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/huggingface_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/instructor_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/jina_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/mistral_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/ollama_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/onnx_mini_lm_l6_v2.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/open_clip_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/openai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/roboflow_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/sentence_transformer_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/text2vec_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/together_ai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/__pycache__/voyageai_embedding_function.cpython-311.pyc,,
chromadb/utils/embedding_functions/amazon_bedrock_embedding_function.py,sha256=SNODvsUB2KZ5BQMasoZZ_pnGlX4A1z0IoBKBRXDSE6c,4925
chromadb/utils/embedding_functions/baseten_embedding_function.py,sha256=Dl2dCGAt0iuW6_qF9TB1Ir_G2SBfwucW1VwoBhhB4QQ,3623
chromadb/utils/embedding_functions/chroma_langchain_embedding_function.py,sha256=YjXqx73a8IWHc0HTGn5Ocvk_e9hz-5mxR36L73ym2ZA,6329
chromadb/utils/embedding_functions/cloudflare_workers_ai_embedding_function.py,sha256=lLlB6hzP8SWlI3yvjkcE7NHEUnZQNl_C76vseDdt2Bk,5379
chromadb/utils/embedding_functions/cohere_embedding_function.py,sha256=7Emly27fPFH7le9mfBLkYAPl7sePd_bkljCcY15jbGs,6684
chromadb/utils/embedding_functions/google_embedding_function.py,sha256=pGtmfYp6bcMOFiXVGzp2L5-wc1BTy6HFFxr2a97kRmg,14730
chromadb/utils/embedding_functions/huggingface_embedding_function.py,sha256=ZMf61bq7_P79Bi2hw9mBSLPhq5mHbGkUkJt1DZVvt6s,8638
chromadb/utils/embedding_functions/instructor_embedding_function.py,sha256=Rlf0VQL-9Tak58TnabpnBToR2j3AEqQ_-IClS-06-XQ,4362
chromadb/utils/embedding_functions/jina_embedding_function.py,sha256=KXgps9zau40KwLxNMhDCPMPhfdaIMaxBrAD6LgbXfG4,7706
chromadb/utils/embedding_functions/mistral_embedding_function.py,sha256=lLBSsEICieNrdZf7V2Yc0kc7_fAgNBs6NeHFndkhfbg,3215
chromadb/utils/embedding_functions/ollama_embedding_function.py,sha256=i8f7uyNoqdlUa9x7iyPLl_dZ-xBAJ19QT8WmFIynncQ,4144
chromadb/utils/embedding_functions/onnx_mini_lm_l6_v2.py,sha256=6ptVqg9x-Wrm0S09ClTRC7ab7UY0vKJFCf0ml6bzfhM,13928
chromadb/utils/embedding_functions/open_clip_embedding_function.py,sha256=aBrD1OFYmUK-yejtd4LW8E1ekUVfN9VlVKVbOO97imw,6257
chromadb/utils/embedding_functions/openai_embedding_function.py,sha256=-rgVVBjMSYcEMyYmNGev-iQcEo-wkojJhiX9fYN_0Io,8425
chromadb/utils/embedding_functions/roboflow_embedding_function.py,sha256=8LdSVn6OKbM94lxOOsMQ1iBHpHVfxmtPj4m7GaRRey4,5174
chromadb/utils/embedding_functions/schemas/README.md,sha256=WqzNpOY4DEeibmS0OsGnoymJEs8ZfvLTZA7Te68r6a4,1700
chromadb/utils/embedding_functions/schemas/__init__.py,sha256=_HxywvDCUeM-ur3dDO3Uw24McobRp5nhjgxVl4PQqEY,488
chromadb/utils/embedding_functions/schemas/__pycache__/__init__.cpython-311.pyc,,
chromadb/utils/embedding_functions/schemas/__pycache__/registry.cpython-311.pyc,,
chromadb/utils/embedding_functions/schemas/__pycache__/schema_utils.cpython-311.pyc,,
chromadb/utils/embedding_functions/schemas/registry.py,sha256=jErKQ0kGFl9njyO3qPC9J6VwY8-JXdxHOCkm9_08L04,1654
chromadb/utils/embedding_functions/schemas/schema_utils.py,sha256=rdXMgZMjEkqM3yiXwVBSloIWUG4Pm7hZF6qIldwcAL4,2675
chromadb/utils/embedding_functions/sentence_transformer_embedding_function.py,sha256=Xbs3vn2abrxCyuUXrRr0pjpco32ne7CwGELl_EioEKE,4700
chromadb/utils/embedding_functions/text2vec_embedding_function.py,sha256=CzRI4pkdSidHiE8-CPXAbYYIHFJLW3Y5_a_M-jC5Vwc,3171
chromadb/utils/embedding_functions/together_ai_embedding_function.py,sha256=I1VQhWhChymVU6Yf0t0gxGjIWtodGEBo5gO4P04eKtM,4605
chromadb/utils/embedding_functions/voyageai_embedding_function.py,sha256=AXpwlT32se3NpvZI-04Qis3qJww8ikX9x5dMfmMNzrg,4018
chromadb/utils/fastapi.py,sha256=AtADn2l-t_2oi0a8f2G0SRFoPjP_Mvd-kAKDnbpDF4A,522
chromadb/utils/lru_cache.py,sha256=T6l1P1n8ym5tq_UwHxLKbCdOq41v4eHEbNvxuRDrIrQ,1108
chromadb/utils/messageid.py,sha256=0lpBGLqnArIgtZFj7_KTsTHnD9F4wSHw9DKn2weM7_0,280
chromadb/utils/read_write_lock.py,sha256=VmH6S-Yxo2cU8rN4h5tz5ifEN-SOuMvPszEDJkdGHbc,2084
chromadb/utils/rendezvous_hash.py,sha256=LcKIkQ1i1xxLBGsJV_Uo9n-5issp6HIdv9NW1spYa_4,2223
chromadb/utils/results.py,sha256=0TVZZz7rTPrK5Z8pYR1yFwBjALMaMZPcYqs-CUq_IaY,3994
chromadb_rust_bindings/__init__.py,sha256=EQHYniO5ZjnemXFkmB-faa2gCtR8HIke29q6_jaFtkg,171
chromadb_rust_bindings/__pycache__/__init__.cpython-311.pyc,,
chromadb_rust_bindings/chromadb_rust_bindings.pyd,sha256=A8ipCTnI4gsFaDfkMuuqakET_h-VLPbaCfFgOtcXkEw,55018496
schemas/embedding_functions/README.md,sha256=fLTxt_7M4ivVlawDYFoC2Ns_8bNS0xyEHTb1QfuIL-c,2025
schemas/embedding_functions/amazon_bedrock.json,sha256=PiV1csj5SV7xPn-mkw8-6OkO3rZym50pOZEcMqZiZ8M,758
schemas/embedding_functions/base_schema.json,sha256=qCTYjteOlehvXgpIisKThkoe35ahpkO19-Cm7QcPoHI,760
schemas/embedding_functions/chroma_langchain.json,sha256=Cvr8zbX6Xo_KQJfZ4hz8F7APmR-SyxkXsYCDbRuHWBk,495
schemas/embedding_functions/cloudflare_workers_ai.json,sha256=mzmf6zKNNDpBiFqRaq9QOboewjje73HJASEKS9HXOoo,1094
schemas/embedding_functions/cohere.json,sha256=p0FIdFDd1PMYs7zBDsjStStlfwq81UXzF0qRtqFygaE,703
schemas/embedding_functions/default.json,sha256=VVIEEUFMFgG_pJarvh-Am3iZsdtmjF8OYOiMDfYZzeo,306
schemas/embedding_functions/google_generative_ai.json,sha256=6O0jHBpHwrMiTQJX6sZgQWwXvbKqD_cxTjz7H6X_jP8,832
schemas/embedding_functions/google_palm.json,sha256=W-1c7nNbTgfmmZ58knikYy18WEGkbnnH2XIKFoSK6UU,650
schemas/embedding_functions/google_vertex.json,sha256=n-goY_lF8mXuitGM0-9APwksvQ9Px2SCKPYWedR9JZk,894
schemas/embedding_functions/huggingface.json,sha256=WdUNPbGax-Lhsg7vJFhLcgotQxiLCzJJ6cTxEckqNz8,718
schemas/embedding_functions/huggingface_server.json,sha256=dSy4pDYilqiOZ-JWBNY_BNa98V-UJa5F7MuSLohYNGU,671
schemas/embedding_functions/instructor.json,sha256=n2uWWNUCg5A2-IZxF_LPA9MZHZutaU_HBfQQ1_JBPaM,757
schemas/embedding_functions/jina.json,sha256=hZVZOPuqtynNktci2inrteIUnb6RszqC-D4JKMIAEng,1409
schemas/embedding_functions/ollama.json,sha256=CaSlvOeRiZ7gLd26A1tydd4tIZl9A1zysRUS8E3siTU,678
schemas/embedding_functions/onnx_mini_lm_l6_v2.json,sha256=nMYfJ1htgFn_Ciz6mDaKmlat0OnbSPHmfbGkBpcXMyo,547
schemas/embedding_functions/open_clip.json,sha256=ghhyzZS597Dv2mJz74q8KYf-z23OyC_eYVwUzfUsnJg,769
schemas/embedding_functions/openai.json,sha256=jyne1EXhMFZy7fSFpbxbWB3iknHd22ma5cEzi7HzqUQ,2076
schemas/embedding_functions/roboflow.json,sha256=iKZUOAfiPBBpI6ioLhmqfHX7neX3HHGI9D-oNFZYnGI,628
schemas/embedding_functions/sentence_transformer.json,sha256=NOXMNjNQbbmNH-LWW31lsWbJSr-RAj3PFAuTqDREUCE,1285
schemas/embedding_functions/text2vec.json,sha256=ZK2jKB7c0cYJSPd1lrCWODrq8OJOnV-pdXun0uqheE8,468
schemas/embedding_functions/together_ai.json,sha256=LQ6v1K8nG2uzDNm92PTDeG9jmcOWdVQ6p3NltPhNs8U,722
schemas/embedding_functions/transformers.json,sha256=_qxdfcEbBy6tsa0eqVU4gURmY1lxkcj3i-94msF0IYU,863
schemas/embedding_functions/voyageai.json,sha256=7mwZEBzhYwTXdEtKbHa--9L5fe3X8PwZCz-B1xA3P4E,637
